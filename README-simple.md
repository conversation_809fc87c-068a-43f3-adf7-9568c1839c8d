# Sistema Semplificato con Bundle OpenTelemetry

Versione semplificata che usa direttamente il bundle OpenTelemetry di Symfony, senza MetricsService personalizzato.

## 🎯 Vantaggi di Questo Approccio

✅ **Più Semplice**: Usa direttamente le API OpenTelemetry  
✅ **Stateless**: Symfony non mantiene stato, invia solo eventi  
✅ **Standard**: Segue le best practice OpenTelemetry  
✅ **Automatico**: Export automatico ogni 5 secondi  
✅ **Meno Codice**: Niente servizi personalizzati  

## 🚀 Avvio Rapido

### 1. Avvia il Stack di Monitoring
```bash
docker-compose -f docker-compose.monitoring.yml up -d
```

### 2. Avvia Symfony
```bash
symfony server:start
# oppure
php -S localhost:8000 -t public/
```

### 3. Testa le Registrazioni
```bash
# Registrazione privato
curl -X POST http://localhost:8000/simple/register/private \
  -d "email=<EMAIL>&source=web&campaign=test"

# Registrazione azienda  
curl -X POST http://localhost:8000/simple/register/business \
  -d "email=<EMAIL>&company=Test Corp&source=web"

# Test multiplo
curl http://localhost:8000/simple/test-registrations

# Test singolo
curl "http://localhost:8000/simple/test-single?type=privato"
```

## 📊 Come Funziona

### Nel Controller
```php
// Ottieni il meter dal bundle
$this->meter = $meterProvider->getMeter('user-registrations', '1.0.0');

// Crea counter
$counter = $this->meter->createCounter(
    'user_registrations_total',
    'count',
    'Total number of user registrations'
);

// Incrementa di 1 con label
$counter->add(1, [
    'user_type' => 'privato',
    'source' => 'web',
    'campaign' => 'test'
]);
```

### Configurazione Bundle
```yaml
# config/packages/otel_sdk.yaml
otel_sdk:
    enabled: true
    metrics:
        enabled: true
        exporters:
            otlp:
                endpoint: "%env(OTEL_EXPORTER_OTLP_ENDPOINT)%/v1/metrics"
        readers:
            - exporter: otlp
              interval: 5000  # Export ogni 5 secondi
```

## 📈 Metriche in Prometheus

La metrica sarà visibile come: `user_registrations_total`

### Query Principali
```promql
# Totale registrazioni per tipo
sum by (user_type) (user_registrations_total)

# Rate orario
sum by (user_type) (rate(user_registrations_total[1h]))

# Incremento giornaliero
sum by (user_type) (increase(user_registrations_total[1d]))
```

## 🔧 Endpoint API

- `POST /simple/register/private` - Registra utente privato
- `POST /simple/register/business` - Registra azienda
- `POST /simple/register` - Registrazione generica
- `GET /simple/test-registrations` - Test multiplo
- `GET /simple/test-single?type=privato` - Test singolo

## 🎛️ Configurazione

### Variabili Ambiente (.env)
```env
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318
```

### Bundle Config (otel_sdk.yaml)
- **Export Interval**: 5 secondi (configurabile)
- **Endpoint**: Automatico da variabile ambiente
- **Resource**: Service name, version, namespace

## 🔍 Debug

### Verifica Metriche in Prometheus
```bash
# Controlla se la metrica esiste
curl "http://localhost:9090/api/v1/query?query=user_registrations_total"

# Verifica rate
curl "http://localhost:9090/api/v1/query?query=rate(user_registrations_total[5m])"
```

### Log Bundle OpenTelemetry
Il bundle logga automaticamente gli export. Controlla i log Symfony:
```bash
tail -f var/log/dev.log | grep -i otel
```

## 🆚 Differenze con MetricsService

| Aspetto | MetricsService | Bundle OpenTelemetry |
|---------|----------------|---------------------|
| **Complessità** | Alta | Bassa |
| **Codice Custom** | Molto | Minimo |
| **Export** | Manuale | Automatico |
| **Configurazione** | Complessa | Semplice |
| **Manutenzione** | Alta | Bassa |
| **Standard** | Custom | OpenTelemetry |

## 🎯 Raccomandazione

**Usa questo approccio** se vuoi:
- Soluzione semplice e standard
- Meno codice da mantenere  
- Export automatico
- Seguire le best practice OpenTelemetry

Il bundle gestisce automaticamente:
- ✅ Aggregation temporality corretta
- ✅ Export periodico
- ✅ Gestione errori
- ✅ Resource attributes
- ✅ Batching e ottimizzazioni
