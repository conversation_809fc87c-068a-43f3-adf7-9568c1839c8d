{"nyholm/psr7": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "4a8c0345442dcca1d8a2c65633dcf0285dd5a5a2"}, "files": ["config/packages/nyholm_psr7.yaml"]}, "php-http/discovery": {"version": "1.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["config/packages/http_discovery.yaml"]}, "symfony/console": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/flex": {"version": "2.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/framework-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.3", "ref": "5a1497d539f691b96afd45ae397ce5fe30beb4b9"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php", ".editorconfig"]}, "symfony/routing": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}}