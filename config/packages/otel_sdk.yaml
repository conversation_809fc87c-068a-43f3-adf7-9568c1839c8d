otel_sdk:
    enabled: true
    resource:
        attributes:
            service.name: "symfony-otlp-app"
            service.version: "1.0.0"
            service.namespace: "test-otlp"
            deployment.environment: "%kernel.environment%"
    trace:
        enabled: false
    metrics:
        enabled: true
        exporters:
            otlp:
                endpoint: "%env(OTEL_EXPORTER_OTLP_ENDPOINT)%/v1/metrics"
                headers: []
                timeout: 10
        readers:
            - exporter: otlp
              interval: 5000  # Export ogni 5 secondi
