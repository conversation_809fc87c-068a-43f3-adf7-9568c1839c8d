receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

processors:
  batch:
    timeout: 1s
    send_batch_size: 1024
  
  # Aggiunge attributi di servizio
  resource:
    attributes:
      - key: service.name
        value: symfony-otlp-app
        action: upsert
      - key: service.version
        value: 1.0.0
        action: upsert

  # Filtra e trasforma le metriche
  metricstransform:
    transforms:
      # Rinomina le metriche per Prometheus
      - include: user_registrations_total_events
        match_type: strict
        action: update
        new_name: user_registrations_total

exporters:
  # Esporta a Prometheus
  prometheus:
    endpoint: "0.0.0.0:8889"
    namespace: "symfony"
    const_labels:
      environment: "development"
    
  # Log per debug
  logging:
    loglevel: info

  # Esporta a Prometheus (alternativo)
  prometheusremotewrite:
    endpoint: "http://prometheus:9090/api/v1/write"
    tls:
      insecure: true

service:
  pipelines:
    metrics:
      receivers: [otlp]
      processors: [resource, batch, metricstransform]
      exporters: [prometheus, logging]
  
  extensions: []
  
  telemetry:
    logs:
      level: "info"
    metrics:
      address: 0.0.0.0:8888
