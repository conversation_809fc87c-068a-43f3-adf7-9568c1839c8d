# Sistema di Metriche Registrazioni Utenti con OTLP/Prometheus/Grafana

Questo progetto implementa un sistema completo per tracciare le registrazioni utenti tramite OpenTelemetry (OTLP), con visualizzazione in Grafana tramite Prometheus.

## 🎯 Obiettivo

Tracciare ogni registrazione utente (privato/azienda) come metrica OTLP, permettendo di:
- Visualizzare l'andamento giornaliero delle registrazioni
- Filtrare per tipo utente (privato/azienda)
- Filtrare per data, sorgente, campagna
- Creare dashboard e alert in Grafana

## 🏗️ Architettura

```
Symfony App → OTLP Collector → Prometheus → Grafana
```

1. **Symfony App**: Genera metriche OTLP per ogni registrazione
2. **OTLP Collector**: Riceve e trasforma le metriche
3. **Prometheus**: Memorizza le metriche time-series
4. **Grafana**: Visualizza dashboard e alert

## 🚀 Avvio Rapido

### 1. Avvia il Stack di Monitoring

```bash
# Avvia Prometheus, Grafana e OTLP Collector
docker-compose -f docker-compose.monitoring.yml up -d

# Verifica che i servizi siano attivi
docker-compose -f docker-compose.monitoring.yml ps
```

### 2. Avvia l'App Symfony

```bash
# Installa dipendenze (se non già fatto)
composer install

# Avvia il server di sviluppo
symfony server:start
# oppure
php -S localhost:8000 -t public/
```

### 3. Configura le Variabili d'Ambiente

Crea/modifica il file `.env.local`:

```env
# Endpoint OTLP Collector
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318

# Altre configurazioni...
APP_ENV=dev
APP_SECRET=your-secret-key
```

### 4. Testa le Registrazioni

```bash
# Registrazione utente privato
curl -X POST http://localhost:8000/register/private \
  -d "email=<EMAIL>&name=Test User&source=web&campaign=test"

# Registrazione azienda
curl -X POST http://localhost:8000/register/business \
  -d "email=<EMAIL>&company=Test Corp&source=web&campaign=b2b"

# Test multiplo
curl http://localhost:8000/test-registrations
```

## 📊 Accesso ai Servizi

- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **OTLP Collector**: http://localhost:4318 (endpoint HTTP)
- **App Symfony**: http://localhost:8000

## 🔧 API Endpoints

### Registrazioni
- `POST /register/private` - Registra utente privato
- `POST /register/business` - Registra azienda
- `POST /register` - Registrazione generica (tipo nel payload)

### Test e Debug
- `GET /test-registrations` - Genera registrazioni di test
- `GET /test?type=privato|azienda` - Test con tipo specifico
- `GET /metrics` - Visualizza contatori metriche

## 📈 Metriche Disponibili

### Metrica Principale
- **Nome**: `user_registrations_total_events`
- **Tipo**: Histogram (eventi discreti)
- **Descrizione**: Conta ogni registrazione utente

### Label Disponibili
- `user_type`: "privato" o "azienda"
- `date`: Data registrazione (YYYY-MM-DD)
- `hour`: Ora registrazione (HH)
- `source`: Sorgente (web, mobile, social, etc.)
- `campaign`: Campagna marketing
- `company_size`: Dimensione azienda (solo per tipo "azienda")

## 📊 Query Grafana Principali

```promql
# Registrazioni totali per tipo
sum by (user_type) (user_registrations_total_events)

# Rate giornaliero
sum by (user_type) (increase(user_registrations_total_events[1d]))

# Rate orario
sum by (user_type) (rate(user_registrations_total_events[1h]))

# Solo registrazioni private
sum(rate(user_registrations_total_events{user_type="privato"}[1h]))

# Registrazioni per sorgente
sum by (source, user_type) (rate(user_registrations_total_events[1h]))
```

Vedi `docs/grafana-queries.md` per query complete e configurazione dashboard.

## 🧪 Test Automatici

Usa lo script di test per generare dati di esempio:

```bash
# Rendi eseguibile
chmod +x docs/test-registrations.sh

# Esegui test
./docs/test-registrations.sh
```

## 🔍 Troubleshooting

### Metriche Non Visibili in Prometheus

1. Verifica che OTLP Collector sia attivo:
   ```bash
   curl http://localhost:4318/v1/metrics
   ```

2. Controlla i log del collector:
   ```bash
   docker-compose -f docker-compose.monitoring.yml logs otel-collector
   ```

3. Verifica endpoint Prometheus:
   ```bash
   curl http://localhost:8889/metrics
   ```

### App Non Invia Metriche

1. Controlla la configurazione OTLP:
   ```bash
   echo $OTEL_EXPORTER_OTLP_ENDPOINT
   ```

2. Verifica i log Symfony:
   ```bash
   tail -f var/log/dev.log
   ```

3. Testa manualmente l'endpoint:
   ```bash
   curl -X POST http://localhost:4318/v1/metrics \
     -H "Content-Type: application/json" \
     -d '{"test": "data"}'
   ```

### Grafana Non Mostra Dati

1. Verifica datasource Prometheus in Grafana
2. Controlla che Prometheus stia raccogliendo metriche:
   ```bash
   curl "http://localhost:9090/api/v1/query?query=user_registrations_total_events"
   ```

## 📁 Struttura File

```
├── src/
│   ├── Controller/
│   │   ├── RegistrationController.php  # API registrazioni
│   │   └── TestController.php          # Test endpoint
│   └── Service/
│       └── MetricsService.php          # Servizio metriche OTLP
├── docs/
│   ├── grafana-queries.md              # Query e dashboard Grafana
│   ├── api-examples.md                 # Esempi API e test
│   └── test-registrations.sh           # Script test automatico
├── grafana/
│   └── provisioning/                   # Configurazione Grafana
├── docker-compose.monitoring.yml       # Stack monitoring
├── otel-collector-config.yaml         # Config OTLP Collector
└── prometheus.yml                      # Config Prometheus
```

## 🎛️ Configurazione Avanzata

### Personalizza Metriche

Modifica `MetricsService::recordUserRegistration()` per aggiungere label personalizzate:

```php
$this->metricsService->recordUserRegistration('privato', [
    'source' => 'mobile_app',
    'campaign' => 'summer_2024',
    'region' => 'italy',
    'device_type' => 'ios'
]);
```

### Alert Personalizzati

Aggiungi regole di alert in Grafana:

```promql
# Alert: Poche registrazioni
sum(rate(user_registrations_total_events[1h])) < 0.1

# Alert: Troppi utenti privati
(sum(rate(user_registrations_total_events{user_type="privato"}[1h])) / 
 sum(rate(user_registrations_total_events[1h]))) > 0.9
```

## 📚 Documentazione Aggiuntiva

- [Query Grafana Complete](docs/grafana-queries.md)
- [Esempi API e Test](docs/api-examples.md)
- [OpenTelemetry PHP SDK](https://opentelemetry.io/docs/languages/php/)
- [Prometheus Query Language](https://prometheus.io/docs/prometheus/latest/querying/basics/)
