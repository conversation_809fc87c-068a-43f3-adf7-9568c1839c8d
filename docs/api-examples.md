# Esempi API per Test Registrazioni

Questo documento contiene esempi di chiamate API per testare il sistema di metriche delle registrazioni.

## Endpoint Disponibili

### 1. Registrazione Utente Privato
```bash
curl -X POST http://localhost:8000/register/private \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=<EMAIL>&name=<PERSON>&source=web&campaign=summer2024"
```

### 2. Registrazione Azienda
```bash
curl -X POST http://localhost:8000/register/business \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=<EMAIL>&company=Azienda SpA&vat=*************&source=web&campaign=b2b_campaign&company_size=medium"
```

### 3. Registrazione Generica (con tipo nel payload)
```bash
# Utente privato
curl -X POST http://localhost:8000/register \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "type=privato&email=<EMAIL>&name=Test User&source=mobile&campaign=app_launch"

# Azienda
curl -X POST http://localhost:8000/register \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "type=azienda&email=<EMAIL>&company=Test Corp&vat=*************&source=web&campaign=enterprise&company_size=large"
```

### 4. Test Endpoint (genera registrazioni multiple)
```bash
curl http://localhost:8000/test-registrations
```

### 5. Test Endpoint con Tipo Specifico
```bash
# Test con utente privato
curl "http://localhost:8000/test?type=privato"

# Test con azienda
curl "http://localhost:8000/test?type=azienda"
```

### 6. Visualizza Metriche Correnti
```bash
curl http://localhost:8000/metrics
```

## Script di Test Automatico

Salva questo script come `test-registrations.sh`:

```bash
#!/bin/bash

BASE_URL="http://localhost:8000"

echo "🚀 Avvio test registrazioni..."

# Test registrazioni private
echo "📝 Registrazioni private..."
for i in {1..5}; do
    curl -s -X POST "$BASE_URL/register/private" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "email=user$<EMAIL>&name=User $i&source=web&campaign=test_campaign" \
        | jq '.message'
    sleep 1
done

# Test registrazioni aziende
echo "🏢 Registrazioni aziende..."
for i in {1..3}; do
    curl -s -X POST "$BASE_URL/register/business" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "email=company$<EMAIL>&company=Company $i&vat=IT1234567890$i&source=web&campaign=b2b_test&company_size=small" \
        | jq '.message'
    sleep 1
done

# Test registrazioni miste
echo "🔄 Registrazioni miste..."
types=("privato" "azienda" "privato" "azienda")
sources=("web" "mobile" "web" "social")

for i in {0..3}; do
    type=${types[$i]}
    source=${sources[$i]}
    
    if [ "$type" = "privato" ]; then
        curl -s -X POST "$BASE_URL/register" \
            -H "Content-Type: application/x-www-form-urlencoded" \
            -d "type=$type&email=mixed$<EMAIL>&name=Mixed User $i&source=$source&campaign=mixed_test" \
            | jq '.message'
    else
        curl -s -X POST "$BASE_URL/register" \
            -H "Content-Type: application/x-www-form-urlencoded" \
            -d "type=$type&email=mixedbiz$<EMAIL>&company=Mixed Biz $i&vat=IT9876543210$i&source=$source&campaign=mixed_test&company_size=medium" \
            | jq '.message'
    fi
    sleep 1
done

echo "✅ Test completati!"

# Mostra metriche finali
echo "📊 Metriche finali:"
curl -s "$BASE_URL/metrics" | jq '.'
```

Rendi eseguibile lo script:
```bash
chmod +x test-registrations.sh
./test-registrations.sh
```

## Test con Diversi Scenari

### Scenario 1: Picco di Registrazioni
```bash
# Simula un picco di registrazioni in un breve periodo
for i in {1..20}; do
    curl -s -X POST http://localhost:8000/register/private \
        -d "email=peak$<EMAIL>&source=social&campaign=viral" > /dev/null &
done
wait
```

### Scenario 2: Registrazioni da Diverse Sorgenti
```bash
sources=("web" "mobile" "social" "email" "referral")
for source in "${sources[@]}"; do
    for i in {1..3}; do
        curl -s -X POST http://localhost:8000/register \
            -d "type=privato&email=${source}$<EMAIL>&source=$source&campaign=multi_source" > /dev/null
    done
done
```

### Scenario 3: Mix Aziende di Diverse Dimensioni
```bash
sizes=("small" "medium" "large" "enterprise")
for size in "${sizes[@]}"; do
    for i in {1..2}; do
        curl -s -X POST http://localhost:8000/register/business \
            -d "email=${size}$<EMAIL>&company=${size^} Company $i&company_size=$size&campaign=size_test" > /dev/null
    done
done
```

## Verifica Metriche in Prometheus

Se hai Prometheus in esecuzione, puoi verificare le metriche direttamente:

```bash
# Verifica che le metriche siano presenti
curl -s "http://localhost:9090/api/v1/query?query=user_registrations_total_events" | jq '.'

# Query per registrazioni per tipo
curl -s "http://localhost:9090/api/v1/query?query=sum by (user_type) (user_registrations_total_events)" | jq '.'

# Query per rate orario
curl -s "http://localhost:9090/api/v1/query?query=sum by (user_type) (rate(user_registrations_total_events[1h]))" | jq '.'
```

## Note

1. **jq**: Gli esempi usano `jq` per formattare il JSON. Installalo con `brew install jq` (macOS) o `apt install jq` (Ubuntu)
2. **Timing**: Aggiungi `sleep` tra le chiamate per evitare di sovraccaricare il sistema
3. **Cleanup**: Le metriche sono cumulative, quindi riavvia l'applicazione per resettarle
4. **OTLP Endpoint**: Assicurati che l'endpoint OTLP sia configurato correttamente in `.env`
