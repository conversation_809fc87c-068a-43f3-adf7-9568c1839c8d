# Query Grafana per Metriche Registrazioni Utenti

Questo documento contiene le query Prometheus/PromQL da utilizzare in Grafana per visualizzare le metriche delle registrazioni utenti.

## Metriche Disponibili

La metrica principale è: `user_registration` (eventi singoli DELTA)

### Label disponibili:
- `user_type`: "privato" o "azienda"
- `source`: Sorgente della registrazione (web, mobile, etc.)
- `campaign`: Campagna di marketing
- `company_size`: Dimensione azienda (solo per tipo "azienda")

## Query Grafana

### 1. Registrazioni Totali per Tipo (Somma Eventi)
```promql
sum by (user_type) (user_registration)
```

### 2. Registrazioni per Giorno (Incremento Giornaliero)
```promql
sum by (user_type) (increase(user_registration[1d]))
```

### 3. Registrazioni per Ora (Rate Orario)
```promql
sum by (user_type) (rate(user_registration[1h]))
```

### 4. Registrazioni Solo Private
```promql
sum(rate(user_registration{user_type="privato"}[1h]))
```

### 5. Registrazioni Solo Aziende
```promql
sum(rate(user_registration{user_type="azienda"}[1h]))
```

### 6. Registrazioni per Sorgente
```promql
sum by (source, user_type) (rate(user_registrations_total[1h]))
```

### 7. Registrazioni per Campagna
```promql
sum by (campaign, user_type) (rate(user_registrations_total[1h]))
```

### 8. Trend Giornaliero (Ultimi 7 giorni)
```promql
sum by (user_type) (increase(user_registrations_total[1d]))
```

### 9. Distribuzione Oraria (Ultime 24 ore)
```promql
sum by (hour, user_type) (increase(user_registrations_total[1h]))
```

### 10. Registrazioni Aziende per Dimensione
```promql
sum by (company_size) (rate(user_registrations_total{user_type="azienda"}[1h]))
```

## Dashboard Grafana Suggerita

### Panel 1: Registrazioni Totali (Stat)
- Query: `sum(user_registrations_total_events)`
- Visualizzazione: Stat
- Titolo: "Registrazioni Totali"

### Panel 2: Registrazioni per Tipo (Pie Chart)
- Query: `sum by (user_type) (user_registrations_total_events)`
- Visualizzazione: Pie Chart
- Titolo: "Distribuzione per Tipo"

### Panel 3: Trend Giornaliero (Time Series)
- Query: `sum by (user_type) (increase(user_registrations_total_events[1d]))`
- Visualizzazione: Time Series
- Titolo: "Registrazioni Giornaliere"
- Time Range: Last 30 days

### Panel 4: Rate Orario (Time Series)
- Query: `sum by (user_type) (rate(user_registrations_total_events[1h]))`
- Visualizzazione: Time Series
- Titolo: "Rate Registrazioni per Ora"
- Time Range: Last 24 hours

### Panel 5: Registrazioni per Sorgente (Bar Chart)
- Query: `sum by (source, user_type) (increase(user_registrations_total_events[1d]))`
- Visualizzazione: Bar Chart
- Titolo: "Registrazioni per Sorgente (Oggi)"

### Panel 6: Heatmap Oraria (Heatmap)
- Query: `sum by (hour) (increase(user_registrations_total_events[1h]))`
- Visualizzazione: Heatmap
- Titolo: "Distribuzione Oraria"

## Filtri Variabili Grafana

Puoi creare variabili in Grafana per filtrare dinamicamente:

### Variable: user_type
- Type: Query
- Query: `label_values(user_registrations_total_events, user_type)`
- Multi-value: true
- Include All: true

### Variable: source
- Type: Query  
- Query: `label_values(user_registrations_total_events, source)`
- Multi-value: true
- Include All: true

### Variable: campaign
- Type: Query
- Query: `label_values(user_registrations_total_events, campaign)`
- Multi-value: true
- Include All: true

## Utilizzo delle Variabili nelle Query

Con le variabili definite, puoi modificare le query così:

```promql
sum by (user_type) (rate(user_registrations_total_events{user_type=~"$user_type", source=~"$source", campaign=~"$campaign"}[1h]))
```

## Alert Rules

### Alert: Poche Registrazioni
```promql
sum(rate(user_registrations_total_events[1h])) < 0.1
```
Scatta se ci sono meno di 0.1 registrazioni all'ora (circa 1 ogni 10 ore).

### Alert: Squilibrio Tipo Utente
```promql
(sum(rate(user_registrations_total_events{user_type="privato"}[1h])) / sum(rate(user_registrations_total_events[1h]))) > 0.9
```
Scatta se più del 90% delle registrazioni sono di tipo "privato".

## Note Tecniche

1. **Counter vs Histogram**: La metrica usa counter monotono perché conta eventi discreti (registrazioni)
2. **Rate vs Increase**: Usa `rate()` per velocità istantanea, `increase()` per conteggi su periodo
3. **Aggregation Temporality**: I counter sono configurati con CUMULATIVE temporality
4. **Monotonia**: I counter non si resettano mai, crescono sempre (monotoni)
