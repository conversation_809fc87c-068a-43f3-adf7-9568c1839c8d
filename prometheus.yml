global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Scrape Prometheus stesso
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Scrape OpenTelemetry Collector
  - job_name: 'otel-collector'
    static_configs:
      - targets: ['otel-collector:8889']
    scrape_interval: 5s
    metrics_path: /metrics

  # Scrape metriche interne del collector
  - job_name: 'otel-collector-internal'
    static_configs:
      - targets: ['otel-collector:8888']
    scrape_interval: 10s

  # Se hai l'app Symfony che espone metriche direttamente
  # - job_name: 'symfony-app'
  #   static_configs:
  #     - targets: ['host.docker.internal:8000']
  #   metrics_path: /metrics
  #   scrape_interval: 10s
