<?php

namespace App\Service;

use Symfony\Contracts\HttpClient\HttpClientInterface;
use Psr\Log\LoggerInterface;

class MetricsService
{
    private array $counters = [];
    private array $gauges = [];
    private array $histograms = [];

    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly LoggerInterface $logger
    ) {
    }

    public function recordEvent(string $name, array $labels = [], float $value = 1.0): void
    {
        // Usa histogram per eventi che si resettano ad ogni export
        // Ogni evento è un "sample" nel histogram
        $this->recordHistogram($name . '_events', $value, $labels);
    }

    public function setGauge(string $name, float $value, array $labels = []): void
    {
        $key = $name . '_' . md5(json_encode($labels));
        $this->gauges[$key] = [
            'name' => $name,
            'value' => $value,
            'labels' => $labels,
            'type' => 'gauge'
        ];
    }

    public function recordHistogram(string $name, float $value, array $labels = []): void
    {
        $key = $name . '_' . md5(json_encode($labels));
        if (!isset($this->histograms[$key])) {
            $this->histograms[$key] = [
                'name' => $name,
                'values' => [],
                'labels' => $labels,
                'type' => 'histogram'
            ];
        }
        $this->histograms[$key]['values'][] = $value;
    }

    public function recordHttpRequest(string $method, string $path, int $statusCode, float $duration): void
    {
        // Event per ogni richiesta HTTP
        $this->recordEvent('http_requests_total', [
            'method' => $method,
            'path' => $path,
            'status_code' => (string)$statusCode
        ]);

        // Histogram per la durata delle richieste
        $this->recordHistogram('http_request_duration_seconds', $duration, [
            'method' => $method,
            'path' => $path
        ]);

        // Gauge per l'ultima durata della richiesta
        $this->setGauge('http_request_last_duration_seconds', $duration, [
            'method' => $method,
            'path' => $path
        ]);
    }

    public function recordBusinessMetric(string $name, float $value, array $labels = []): void
    {
        $this->setGauge($name, $value, $labels);
    }

    /**
     * Registra un evento di registrazione utente
     *
     * @param string $userType Tipo di utente: 'privato' o 'azienda'
     * @param array $additionalLabels Label aggiuntive opzionali (es. source, campaign, etc.)
     */
    public function recordUserRegistration(string $userType, array $additionalLabels = []): void
    {
        // Valida il tipo utente
        if (!in_array($userType, ['privato', 'azienda'])) {
            throw new \InvalidArgumentException("User type must be 'privato' or 'azienda', got: $userType");
        }

        // Prepara le label base
        $labels = array_merge([
            'user_type' => $userType,
            'date' => date('Y-m-d'), // Per facilitare i filtri giornalieri
            'hour' => date('H'),     // Per analisi orarie
        ], $additionalLabels);

        // Registra l'evento di registrazione
        $this->recordEvent('user_registrations_total', $labels);

        // Log per debug
        $this->logger->info('User registration recorded', [
            'user_type' => $userType,
            'labels' => $labels
        ]);
    }

    public function exportToPrometheus(): void
    {
        $otlpEndpoint = $_ENV['OTEL_EXPORTER_OTLP_ENDPOINT'] ?? 'http://localhost:4318';
        $metricsEndpoint = $otlpEndpoint . '/v1/metrics';

        $payload = $this->buildOTLPMetricsPayload();

        // Debug: log payload prima dell'invio
        $this->logger->info('Sending OTLP payload', [
            'endpoint' => $metricsEndpoint,
            'payload_size' => strlen(json_encode($payload)),
            'counters_count' => count($this->counters),
            'gauges_count' => count($this->gauges),
            'histograms_count' => count($this->histograms)
        ]);

        try {
            $response = $this->httpClient->request('POST', $metricsEndpoint, [
                'headers' => [
                    'Content-Type' => 'application/json'
                ],
                'json' => $payload
            ]);

            $statusCode = $response->getStatusCode();
            $responseBody = $response->getContent(false);

            $this->logger->info('OTLP Response received', [
                'status_code' => $statusCode,
                'response_body' => $responseBody,
                'counters' => count($this->counters),
                'gauges' => count($this->gauges),
                'histograms' => count($this->histograms)
            ]);

            if ($statusCode === 200) {
                $this->logger->info('Metrics exported successfully');
                // NON resettiamo i counter - devono rimanere cumulativi per rate()/increase()
                $this->histograms = []; // Reset only histograms
                // I counter rimangono per essere cumulativi
            } else {
                $this->logger->error('OTLP export failed', [
                    'status_code' => $statusCode,
                    'response' => $responseBody
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('Failed to export metrics: ' . $e->getMessage(), [
                'exception' => $e::class,
                'endpoint' => $metricsEndpoint
            ]);
        }
    }

    private function buildOTLPMetricsPayload(): array
    {
        $metrics = [];
        $now = time() * 1_000_000_000; // nanoseconds

        // Counters
        foreach ($this->counters as $counter) {
            $metrics[] = [
                'name' => $counter['name'],
                'unit' => '',
                'sum' => [
                    'dataPoints' => [
                        [
                            'attributes' => $this->formatLabels($counter['labels']),
                            'timeUnixNano' => $now,
                            'asDouble' => $counter['value'],
                            'flags' => 0
                        ]
                    ],
                    'aggregationTemporality' => 1, // DELTA (per eventi singoli)
                    'isMonotonic' => true
                ]
            ];
        }

        // Gauges
        foreach ($this->gauges as $gauge) {
            $metrics[] = [
                'name' => $gauge['name'],
                'unit' => '',
                'gauge' => [
                    'dataPoints' => [
                        [
                            'attributes' => $this->formatLabels($gauge['labels']),
                            'timeUnixNano' => $now,
                            'asDouble' => $gauge['value'],
                            'flags' => 0
                        ]
                    ]
                ]
            ];
        }

        // Histograms
        foreach ($this->histograms as $histogram) {
            $values = $histogram['values'];
            sort($values);
            $count = count($values);
            $sum = array_sum($values);

            $metrics[] = [
                'name' => $histogram['name'],
                'unit' => '',
                'histogram' => [
                    'dataPoints' => [
                        [
                            'attributes' => $this->formatLabels($histogram['labels']),
                            'timeUnixNano' => $now,
                            'count' => $count,
                            'sum' => $sum,
                            'bucketCounts' => $this->calculateBuckets($values),
                            'explicitBounds' => [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
                            'flags' => 0
                        ]
                    ],
                    'aggregationTemporality' => 2
                ]
            ];
        }

        return [
            'resourceMetrics' => [
                [
                    'resource' => [
                        'attributes' => [
                            ['key' => 'service.name', 'value' => ['stringValue' => 'symfony-otlp-app']],
                            ['key' => 'service.version', 'value' => ['stringValue' => '1.0.0']]
                        ]
                    ],
                    'scopeMetrics' => [
                        [
                            'scope' => [
                                'name' => 'symfony-metrics',
                                'version' => '1.0.0'
                            ],
                            'metrics' => $metrics
                        ]
                    ]
                ]
            ]
        ];
    }

    private function formatLabels(array $labels): array
    {
        return array_map(
            fn($k, $v) => ['key' => $k, 'value' => ['stringValue' => (string)$v]],
            array_keys($labels),
            array_values($labels)
        );
    }

    private function calculateBuckets(array $values): array
    {
        $bounds = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10];
        $buckets = array_fill(0, count($bounds) + 1, 0);

        foreach ($values as $value) {
            $bucketIndex = 0;
            foreach ($bounds as $i => $bound) {
                if ($value <= $bound) {
                    $bucketIndex = $i;
                    break;
                }
                $bucketIndex = $i + 1;
            }
            $buckets[$bucketIndex]++;
        }

        return $buckets;
    }

    public function getMetricsCount(): array
    {
        return [
            'counters' => count($this->counters),
            'gauges' => count($this->gauges),
            'histograms' => count($this->histograms)
        ];
    }
}
