<?php

namespace App\Service;

use OpenTelemetry\API\Metrics\CounterInterface;
use OpenTelemetry\API\Metrics\MeterProviderInterface;

class RegistrationMetrics
{
    private CounterInterface $registrationCounter;

    public function __construct(MeterProviderInterface $meterProvider)
    {
        $meter = $meterProvider->getMeter('user-registrations', '1.0.0');
        
        // Crea il counter UNA SOLA VOLTA nel costruttore
        $this->registrationCounter = $meter->createCounter(
            'user_registrations_total',
            'count',
            'Total number of user registrations'
        );
    }

    /**
     * Registra un evento di registrazione utente
     */
    public function recordRegistration(string $userType, array $attributes = []): void
    {
        // Valida il tipo utente
        if (!in_array($userType, ['privato', 'azienda'])) {
            throw new \InvalidArgumentException("User type must be 'privato' or 'azienda', got: $userType");
        }

        // Prepara gli attributi base
        $finalAttributes = array_merge([
            'user_type' => $userType,
        ], $attributes);

        // Incrementa il counter (già creato)
        $this->registrationCounter->add(1, $finalAttributes);
    }

    /**
     * Registra una registrazione privata
     */
    public function recordPrivateRegistration(array $attributes = []): void
    {
        $this->recordRegistration('privato', $attributes);
    }

    /**
     * Registra una registrazione aziendale
     */
    public function recordBusinessRegistration(array $attributes = []): void
    {
        $this->recordRegistration('azienda', $attributes);
    }
}
