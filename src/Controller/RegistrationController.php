<?php

namespace App\Controller;

use App\Service\MetricsService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

class RegistrationController extends AbstractController
{
    public function __construct(private readonly MetricsService $metricsService)
    {
    }

    /**
     * Simula la registrazione di un utente privato
     */
    #[Route('/register/private', name: 'register_private', methods: ['POST'])]
    public function registerPrivate(Request $request): JsonResponse
    {
        // Simula la logica di registrazione
        $userData = [
            'email' => $request->request->get('email', '<EMAIL>'),
            'name' => $request->request->get('name', 'Test User'),
            'type' => 'privato'
        ];

        // Registra la metrica di registrazione (senza export automatico)
        $this->metricsService->recordUserRegistration('privato', [
            'source' => $request->request->get('source', 'web'),
            'campaign' => $request->request->get('campaign', 'organic')
        ]);

        return $this->json([
            'success' => true,
            'message' => 'Utente privato registrato con successo',
            'user' => $userData,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Simula la registrazione di un utente azienda
     */
    #[Route('/register/business', name: 'register_business', methods: ['POST'])]
    public function registerBusiness(Request $request): JsonResponse
    {
        // Simula la logica di registrazione
        $userData = [
            'email' => $request->request->get('email', '<EMAIL>'),
            'company' => $request->request->get('company', 'Test Company'),
            'vat' => $request->request->get('vat', '*************'),
            'type' => 'azienda'
        ];

        // Registra la metrica di registrazione (senza export automatico)
        $this->metricsService->recordUserRegistration('azienda', [
            'source' => $request->request->get('source', 'web'),
            'campaign' => $request->request->get('campaign', 'organic'),
            'company_size' => $request->request->get('company_size', 'small')
        ]);

        return $this->json([
            'success' => true,
            'message' => 'Azienda registrata con successo',
            'user' => $userData,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Endpoint generico per registrazione (con tipo specificato nel payload)
     */
    #[Route('/register', name: 'register_generic', methods: ['POST'])]
    public function register(Request $request): JsonResponse
    {
        $userType = $request->request->get('type');

        if (!$userType || !in_array($userType, ['privato', 'azienda'])) {
            return $this->json([
                'success' => false,
                'error' => 'Tipo utente non valido. Deve essere "privato" o "azienda"'
            ], 400);
        }

        // Simula la logica di registrazione
        $userData = [
            'email' => $request->request->get('email', '<EMAIL>'),
            'type' => $userType
        ];

        if ($userType === 'azienda') {
            $userData['company'] = $request->request->get('company', 'Default Company');
            $userData['vat'] = $request->request->get('vat', '*************');
        } else {
            $userData['name'] = $request->request->get('name', 'Default User');
        }

        // Registra la metrica di registrazione
        $additionalLabels = [
            'source' => $request->request->get('source', 'web'),
            'campaign' => $request->request->get('campaign', 'organic')
        ];

        if ($userType === 'azienda') {
            $additionalLabels['company_size'] = $request->request->get('company_size', 'small');
        }

        $this->metricsService->recordUserRegistration($userType, $additionalLabels);

        return $this->json([
            'success' => true,
            'message' => ucfirst($userType) . ' registrato con successo',
            'user' => $userData,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Endpoint per testare rapidamente le registrazioni
     */
    #[Route('/test-registrations', name: 'test_registrations')]
    public function testRegistrations(): JsonResponse
    {
        // Simula alcune registrazioni di test
        $registrations = [];

        // 3 registrazioni private
        for ($i = 0; $i < 3; $i++) {
            $this->metricsService->recordUserRegistration('privato', [
                'source' => $i % 2 === 0 ? 'web' : 'mobile',
                'campaign' => 'test_campaign'
            ]);
            $registrations[] = ['type' => 'privato', 'source' => $i % 2 === 0 ? 'web' : 'mobile'];
        }

        // 2 registrazioni azienda
        for ($i = 0; $i < 2; $i++) {
            $this->metricsService->recordUserRegistration('azienda', [
                'source' => 'web',
                'campaign' => 'business_campaign',
                'company_size' => $i === 0 ? 'small' : 'medium'
            ]);
            $registrations[] = ['type' => 'azienda', 'company_size' => $i === 0 ? 'small' : 'medium'];
        }

        // Esporta le metriche
        $this->metricsService->exportToPrometheus();

        return $this->json([
            'success' => true,
            'message' => 'Test registrations created',
            'registrations' => $registrations,
            'metrics_count' => $this->metricsService->getMetricsCount(),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Endpoint per esportare manualmente le metriche a OTLP
     */
    #[Route('/export-metrics', name: 'export_metrics')]
    public function exportMetrics(): JsonResponse
    {
        try {
            $this->metricsService->exportToPrometheus();

            return $this->json([
                'success' => true,
                'message' => 'Metrics exported successfully',
                'metrics_count' => $this->metricsService->getMetricsCount(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => 'Failed to export metrics: ' . $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ], 500);
        }
    }

    /**
     * Endpoint per visualizzare i counter correnti
     */
    #[Route('/current-metrics', name: 'current_metrics')]
    public function currentMetrics(): JsonResponse
    {
        return $this->json([
            'metrics_count' => $this->metricsService->getMetricsCount(),
            'counters' => $this->metricsService->getCountersData(),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
