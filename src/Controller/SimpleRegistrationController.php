<?php

namespace App\Controller;

use OpenTelemetry\API\Metrics\MeterProviderInterface;
use OpenTelemetry\API\Metrics\MeterInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

class SimpleRegistrationController extends AbstractController
{
    private MeterInterface $meter;

    public function __construct(MeterProviderInterface $meterProvider)
    {
        $this->meter = $meterProvider->getMeter('user-registrations', '1.0.0');
    }

    /**
     * Registrazione utente privato
     */
    #[Route('/simple/register/private', name: 'simple_register_private', methods: ['POST'])]
    public function registerPrivate(Request $request): JsonResponse
    {
        // Simula la logica di registrazione
        $userData = [
            'email' => $request->request->get('email', '<EMAIL>'),
            'name' => $request->request->get('name', 'Test User'),
            'type' => 'privato'
        ];

        // Crea counter e incrementa di 1
        $counter = $this->meter->createCounter(
            'user_registrations_total',
            'count',
            'Total number of user registrations'
        );

        $counter->add(1, [
            'user_type' => 'privato',
            'source' => $request->request->get('source', 'web'),
            'campaign' => $request->request->get('campaign', 'organic')
        ]);

        return $this->json([
            'success' => true,
            'message' => 'Utente privato registrato con successo',
            'user' => $userData,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Registrazione azienda
     */
    #[Route('/simple/register/business', name: 'simple_register_business', methods: ['POST'])]
    public function registerBusiness(Request $request): JsonResponse
    {
        // Simula la logica di registrazione
        $userData = [
            'email' => $request->request->get('email', '<EMAIL>'),
            'company' => $request->request->get('company', 'Test Company'),
            'vat' => $request->request->get('vat', '*************'),
            'type' => 'azienda'
        ];

        // Crea counter e incrementa di 1
        $counter = $this->meter->createCounter(
            'user_registrations_total',
            'count',
            'Total number of user registrations'
        );

        $counter->add(1, [
            'user_type' => 'azienda',
            'source' => $request->request->get('source', 'web'),
            'campaign' => $request->request->get('campaign', 'organic'),
            'company_size' => $request->request->get('company_size', 'small')
        ]);

        return $this->json([
            'success' => true,
            'message' => 'Azienda registrata con successo',
            'user' => $userData,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Endpoint generico per registrazione
     */
    #[Route('/simple/register', name: 'simple_register_generic', methods: ['POST'])]
    public function register(Request $request): JsonResponse
    {
        $userType = $request->request->get('type');
        
        if (!$userType || !in_array($userType, ['privato', 'azienda'])) {
            return $this->json([
                'success' => false,
                'error' => 'Tipo utente non valido. Deve essere "privato" o "azienda"'
            ], 400);
        }

        // Simula la logica di registrazione
        $userData = [
            'email' => $request->request->get('email', '<EMAIL>'),
            'type' => $userType
        ];

        if ($userType === 'azienda') {
            $userData['company'] = $request->request->get('company', 'Default Company');
            $userData['vat'] = $request->request->get('vat', '*************');
        } else {
            $userData['name'] = $request->request->get('name', 'Default User');
        }

        // Crea counter e incrementa di 1
        $counter = $this->meter->createCounter(
            'user_registrations_total',
            'count',
            'Total number of user registrations'
        );

        $labels = [
            'user_type' => $userType,
            'source' => $request->request->get('source', 'web'),
            'campaign' => $request->request->get('campaign', 'organic')
        ];

        if ($userType === 'azienda') {
            $labels['company_size'] = $request->request->get('company_size', 'small');
        }

        $counter->add(1, $labels);

        return $this->json([
            'success' => true,
            'message' => ucfirst($userType) . ' registrato con successo',
            'user' => $userData,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test endpoint per generare registrazioni multiple
     */
    #[Route('/simple/test-registrations', name: 'simple_test_registrations')]
    public function testRegistrations(): JsonResponse
    {
        $counter = $this->meter->createCounter(
            'user_registrations_total',
            'count',
            'Total number of user registrations'
        );

        $registrations = [];

        // 3 registrazioni private
        for ($i = 0; $i < 3; $i++) {
            $counter->add(1, [
                'user_type' => 'privato',
                'source' => $i % 2 === 0 ? 'web' : 'mobile',
                'campaign' => 'test_campaign'
            ]);
            $registrations[] = ['type' => 'privato', 'source' => $i % 2 === 0 ? 'web' : 'mobile'];
        }

        // 2 registrazioni azienda
        for ($i = 0; $i < 2; $i++) {
            $counter->add(1, [
                'user_type' => 'azienda',
                'source' => 'web',
                'campaign' => 'business_campaign',
                'company_size' => $i === 0 ? 'small' : 'medium'
            ]);
            $registrations[] = ['type' => 'azienda', 'company_size' => $i === 0 ? 'small' : 'medium'];
        }

        return $this->json([
            'success' => true,
            'message' => 'Test registrations created',
            'registrations' => $registrations,
            'count' => count($registrations),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test singolo evento
     */
    #[Route('/simple/test-single', name: 'simple_test_single')]
    public function testSingle(Request $request): JsonResponse
    {
        $userType = $request->query->get('type', 'privato');
        
        if (!in_array($userType, ['privato', 'azienda'])) {
            $userType = 'privato';
        }

        $counter = $this->meter->createCounter(
            'user_registrations_total',
            'count',
            'Total number of user registrations'
        );

        $counter->add(1, [
            'user_type' => $userType,
            'source' => 'test',
            'campaign' => 'single_test'
        ]);

        return $this->json([
            'success' => true,
            'message' => "Single {$userType} registration recorded",
            'user_type' => $userType,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
