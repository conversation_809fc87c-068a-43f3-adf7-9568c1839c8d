<?php

namespace App\Controller;

use App\Service\MetricsService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

class TestController extends AbstractController
{
    public function __construct(private readonly MetricsService $metricsService)
    {
    }

    #[Route('/test', name: 'test')]
    public function test(Request $request): JsonResponse
    {
        $startTime = microtime(true);

        // Ogni login/evento invia 1 singola metrica
        $this->metricsService->recordEvent('Z_user_login_events', [
            'tipo' => 'privato',
            'dimensione' => 'quadrato',
        ]);

//        // Simula alcune metriche business
//        $this->metricsService->setGauge('B', rand(100, 500));
//        $this->metricsService->setGauge('C', rand(80, 99) / 100);
//
//        $duration = microtime(true) - $startTime;
//
//        // Registra metrics HTTP standard
//        $this->metricsService->recordHttpRequest(
//            $request->getMethod(),
//            '/test',
//            200,
//            $duration
//        );
//
//        // Simula processamento con metrics
//        $processingTime = rand(50, 200) / 1000; // 50-200ms
//        usleep($processingTime * 1000000);
//
//        // Registra histogram del tempo di processamento
//        $this->metricsService->recordHistogram('processing_duration_seconds', $processingTime, [
//            'operation' => 'simulate_work'
//        ]);
//
//        // Business metrics
//        $this->metricsService->recordBusinessMetric('database_connections', rand(5, 20));

        // Esporta le metrics a Prometheus
        $this->metricsService->exportToPrometheus();

        return $this->json([
            'message' => 'Test route with metrics collection!',
            'timestamp' => date('Y-m-d H:i:s'),
            'metrics_enabled' => true,
            'metrics_collected' => $this->metricsService->getMetricsCount()
        ]);
    }

    #[Route('/metrics', name: 'metrics')]
    public function metrics(): JsonResponse
    {
        return $this->json([
            'service' => 'symfony-otlp-app',
            'metrics_collected' => $this->metricsService->getMetricsCount(),
            'status' => 'active'
        ]);
    }
}
