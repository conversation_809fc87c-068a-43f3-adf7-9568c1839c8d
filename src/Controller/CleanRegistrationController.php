<?php

namespace App\Controller;

use App\Service\RegistrationMetrics;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

class CleanRegistrationController extends AbstractController
{
    public function __construct(
        private readonly RegistrationMetrics $metrics
    ) {
    }

    /**
     * Registrazione utente privato
     */
    #[Route('/clean/register/private', name: 'clean_register_private', methods: ['POST'])]
    public function registerPrivate(Request $request): JsonResponse
    {
        // Simula la logica di registrazione
        $userData = [
            'email' => $request->request->get('email', '<EMAIL>'),
            'name' => $request->request->get('name', 'Test User'),
            'type' => 'privato'
        ];

        // TODO: Qui ci sarebbe la logica di salvataggio nel database
        // $this->userRepository->save($userData);

        // Registra la metrica (UNA SOLA RIGA!)
        $this->metrics->recordPrivateRegistration([
            'source' => $request->request->get('source', 'web'),
            'campaign' => $request->request->get('campaign', 'organic')
        ]);

        return $this->json([
            'success' => true,
            'message' => 'Utente privato registrato con successo',
            'user' => $userData,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Registrazione azienda
     */
    #[Route('/clean/register/business', name: 'clean_register_business', methods: ['POST'])]
    public function registerBusiness(Request $request): JsonResponse
    {
        // Simula la logica di registrazione
        $userData = [
            'email' => $request->request->get('email', '<EMAIL>'),
            'company' => $request->request->get('company', 'Test Company'),
            'vat' => $request->request->get('vat', '*************'),
            'type' => 'azienda'
        ];

        // TODO: Qui ci sarebbe la logica di salvataggio nel database
        // $this->companyRepository->save($userData);

        // Registra la metrica (UNA SOLA RIGA!)
        $this->metrics->recordBusinessRegistration([
            'source' => $request->request->get('source', 'web'),
            'campaign' => $request->request->get('campaign', 'organic'),
            'company_size' => $request->request->get('company_size', 'small')
        ]);

        return $this->json([
            'success' => true,
            'message' => 'Azienda registrata con successo',
            'user' => $userData,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Endpoint generico per registrazione
     */
    #[Route('/clean/register', name: 'clean_register_generic', methods: ['POST'])]
    public function register(Request $request): JsonResponse
    {
        $userType = $request->request->get('type');
        
        if (!$userType || !in_array($userType, ['privato', 'azienda'])) {
            return $this->json([
                'success' => false,
                'error' => 'Tipo utente non valido. Deve essere "privato" o "azienda"'
            ], 400);
        }

        // Simula la logica di registrazione
        $userData = [
            'email' => $request->request->get('email', '<EMAIL>'),
            'type' => $userType
        ];

        if ($userType === 'azienda') {
            $userData['company'] = $request->request->get('company', 'Default Company');
            $userData['vat'] = $request->request->get('vat', '*************');
        } else {
            $userData['name'] = $request->request->get('name', 'Default User');
        }

        // TODO: Logica di salvataggio nel database

        // Registra la metrica (UNA SOLA RIGA!)
        $attributes = [
            'source' => $request->request->get('source', 'web'),
            'campaign' => $request->request->get('campaign', 'organic')
        ];

        if ($userType === 'azienda') {
            $attributes['company_size'] = $request->request->get('company_size', 'small');
        }

        $this->metrics->recordRegistration($userType, $attributes);

        return $this->json([
            'success' => true,
            'message' => ucfirst($userType) . ' registrato con successo',
            'user' => $userData,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test endpoint per generare registrazioni multiple
     */
    #[Route('/clean/test-registrations', name: 'clean_test_registrations')]
    public function testRegistrations(): JsonResponse
    {
        $registrations = [];

        // 3 registrazioni private
        for ($i = 0; $i < 3; $i++) {
            $this->metrics->recordPrivateRegistration([
                'source' => $i % 2 === 0 ? 'web' : 'mobile',
                'campaign' => 'test_campaign'
            ]);
            $registrations[] = ['type' => 'privato', 'source' => $i % 2 === 0 ? 'web' : 'mobile'];
        }

        // 2 registrazioni azienda
        for ($i = 0; $i < 2; $i++) {
            $this->metrics->recordBusinessRegistration([
                'source' => 'web',
                'campaign' => 'business_campaign',
                'company_size' => $i === 0 ? 'small' : 'medium'
            ]);
            $registrations[] = ['type' => 'azienda', 'company_size' => $i === 0 ? 'small' : 'medium'];
        }

        return $this->json([
            'success' => true,
            'message' => 'Test registrations created',
            'registrations' => $registrations,
            'count' => count($registrations),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test singolo evento
     */
    #[Route('/clean/test-single', name: 'clean_test_single')]
    public function testSingle(Request $request): JsonResponse
    {
        $userType = $request->query->get('type', 'privato');
        
        if (!in_array($userType, ['privato', 'azienda'])) {
            $userType = 'privato';
        }

        // UNA SOLA RIGA per registrare la metrica!
        $this->metrics->recordRegistration($userType, [
            'source' => 'test',
            'campaign' => 'single_test'
        ]);

        return $this->json([
            'success' => true,
            'message' => "Single {$userType} registration recorded",
            'user_type' => $userType,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
